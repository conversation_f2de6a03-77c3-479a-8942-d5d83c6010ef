<%= turbo_stream_from Current.user, llm_stream_channel_id(@request_id) %>
<div class="p-4 flex-1 flex flex-col" data-controller="mode-switcher" data-mode-switcher-current-value="description">
  <!-- Mode Switcher -->
  <div class="mb-4">
    <div class="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 w-full">
      <button type="button" 
              data-action="click->mode-switcher#switchToDescription"
        data-mode-switcher-target="descriptionTab"
        data-mode-active="true"
        class="flex-1 px-3 py-2 rounded-md text-xs font-medium transition-colors data-[mode-active=true]:bg-purple-600 data-[mode-active=true]:text-white data-[mode-active=true]:shadow-sm data-[mode-active=false]:text-purple-600 data-[mode-active=false]:hover:bg-purple-50 data-[mode-active=false]:hover:text-purple-700 dark:data-[mode-active=false]:text-purple-400 dark:data-[mode-active=false]:hover:bg-purple-900/20 dark:data-[mode-active=false]:hover:text-purple-300">
        Simple
      </button>
      <button type="button" 
              data-action="click->mode-switcher#switchToParameters"
        data-mode-switcher-target="parametersTab"
        data-mode-active="false"
        class="flex-1 px-3 py-2 rounded-md text-xs font-medium transition-colors data-[mode-active=true]:bg-purple-600 data-[mode-active=true]:text-white data-[mode-active=true]:shadow-sm data-[mode-active=false]:text-purple-600 data-[mode-active=false]:hover:bg-purple-50 data-[mode-active=false]:hover:text-purple-700 dark:data-[mode-active=false]:text-purple-400 dark:data-[mode-active=false]:hover:bg-purple-900/20 dark:data-[mode-active=false]:hover:text-purple-300">
        Advanced
      </button>
    </div>
    <div class="mt-2 text-gray-600 dark:text-gray-300 text-xs">
      <span data-mode-switcher-target="descriptionHelp">
        💡 Describe the music you want in natural language
      </span>
      <span data-mode-switcher-target="parametersHelp" class="hidden">
        🎛️ Fine-tune specific musical parameters
      </span>
    </div>
  </div>
  <!-- Description Mode Form -->
  <%= render "description_mode_form", 
      request_id: @request_id, 
      current_credits: @current_credits, 
      generation_cost: @generation_cost %>
  <!-- Parameters Mode Form -->
  <%= render "parameters_mode_form", 
      request_id: @request_id, 
      current_credits: @current_credits, 
      generation_cost: @generation_cost %>
</div>
