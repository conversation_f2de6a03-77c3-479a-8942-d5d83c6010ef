<div data-mode-switcher-target="parametersMode" class="hidden">
  <%= form_with url: generations_path, method: :post,
                scope: :generation,
                data: {
                  controller: "generation-form vocal-type",
                  action: "keydown->generation-form#preventEnterSubmit change->vocal-type#vocalTypeChanged"
                },
                class: "space-y-3" do |form| %>
    <!-- Mode hidden field -->
    <%= form.hidden_field :mode, value: "parameters" %>
    <!-- Request ID for tab-specific messaging -->
    <%= form.hidden_field :request_id, value: request_id %>
    <!-- Vocal Type Selection -->
    <%= render "vocal_type_selection", form: form %>
    <!-- Title field -->
    <div class="mb-0">
      <%= form.label :title, "Song Title", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
      <div data-controller="character-counter" data-character-counter-max-value="80">
        <%= form.text_field :title,
                            placeholder: "Enter the title for your song...",
                            maxlength: 80,
                            class: "w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm transition-colors" %>
        <div class="text-right mt-1">
          <span class="text-xs text-gray-500 dark:text-gray-400" data-character-counter-target="counter">0/80 characters</span>
        </div>
      </div>
    </div>
    <!-- Music Style Components -->
    <div class="space-y-4">
      <!-- Genre -->
      <%= render "preset_search_field",
          field_type: "genre",
          label: "Genre",
          placeholder: "Type to search or add a new genre...",
          form: form,
          request_id: request_id,
          required: false %>
      <!-- Mood -->
      <%= render "preset_search_field",
          field_type: "mood",
          label: "Mood",
          placeholder: "Type to search or add a new mood...",
          form: form,
          request_id: request_id,
          required: false %>
    </div>
    <!-- Lyrics -->
    <div data-vocal-type-target="lyricsSection" class="mb-0 mt-6">
      <div class="flex items-center justify-between mb-2">
        <%= form.label :lyrics, "Lyrics", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
        <%= turbo_frame_tag :lyrics_gen_button do %>
          <%= render "shared/lyrics_generation_button_submit" %>
        <% end %>
      </div>
      <div data-controller="character-counter" data-character-counter-max-value="3000">
        <%= form.text_area :lyrics,
                             rows: 4,
                             placeholder: "Enter lyrics or use Generate button...",
                             id: "generation_lyrics",
                             maxlength: 3000,
                             required: false,
                             data: {
                               controller: "auto-resize",
                               vocal_type_target: "lyricsField",
                               auto_resize_max_height_value: 300
                             },
                             class: "w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-300 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none font-mono text-xs transition-colors" %>
        <div class="text-right mt-1">
          <span class="text-xs text-gray-500 dark:text-gray-400" data-character-counter-target="counter">0/3000 characters</span>
        </div>
      </div>
    </div>
    <!-- Advanced Options -->
    <div data-controller="advanced-options" class="">
      <button type="button"
              data-action="click->advanced-options#toggle"
        class="text-sm text-purple-600 hover:text-pink-600 dark:text-purple-400 dark:hover:text-pink-400 font-medium flex items-center space-x-1 transition-colors">
        <span data-advanced-options-target="toggleText">Show Advanced Options</span>
        <svg data-advanced-options-target="toggleIcon" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      <div data-advanced-options-target="content" class="hidden mt-4 space-y-4 p-4 bg-gradient-to-r from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg border border-purple-200/30 dark:border-gray-700">
        <!-- Instruments -->
        <%= render "preset_search_field",
            field_type: "instrument",
            label: "Instruments",
            placeholder: "Type to search or add instruments...",
            form: form,
            request_id: request_id,
            required: false %>
        <!-- Tempo (TPM) -->
        <%= render "preset_search_field",
            field_type: "tpm",
            label: "Tempo (BPM)",
            form: form,
            request_id: request_id,
            multiple: false %>
      </div>
    </div>
    <%= render "form_footer", form: form, current_credits: current_credits, generation_cost: generation_cost %>
  <% end %>
</div>